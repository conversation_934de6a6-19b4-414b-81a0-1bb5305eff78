package com.zsmall.activity.entity.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.activity.entity.domain.dto.productActivity.*;
import com.zsmall.activity.entity.domain.dto.productActivity.export.*;
import com.zsmall.activity.entity.domain.vo.productActivity.SupplierProductActivityVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Set;

/**
 * 供货商商品活动Mapper接口
 *
 * <AUTHOR> Li
 * @date 2025-07-02
 */
@MapperScan
public interface SupplierProductActivityMapper extends BaseMapperPlus<SupplierProductActivity, SupplierProductActivityVo> {

    /**
     * 查询活动列表
     * @param page
     * @param bo
     * @return
     */
    IPage<SupplierProductActivity> selectListResponseDTO(@Param("page") Page page, @Param("bo") ProductActivitySearchDTO bo);

    /**
     * 查询供应商活动详情
     * @param activeCode
     * @return
     */
    List<ProductActivityDetailsDTO> selectSupplierActivityDetailsDTO(Set<String> activeCode);

    /**
     * 查询分销商活动详情
     * @param activeCode
     * @return
     */
    List<ProductActivityDetailsDTO> selectDistributorActivityDetailsDTO(Set<String> activeCode);

    /**
     * 供应商活动导出
     * @param bo
     * @return
     */
    List<SupplierProductActivityListExportDTO> selectSupplierProductActivityListExport(@Param("bo") ProductActivitySearchDTO bo);

    /**
     * 供应商活动仓库导出
     * @param bo
     * @return
     */
    List<AdminProductActivityWarehouseExportDTO> selectProductActivityWarehouseExport( @Param("bo") ProductActivitySearchDTO bo);


    /**
     * 超管活动列表导出
     * @param bo
     * @return
     */
    List<AdminProductActivityListExportDTO> selectAdminProductActivityListExport(@Param("bo") ProductActivitySearchDTO bo);


    /**
     * 超管活动详情导出
     * @param bo
     * @return
     */
    List<AdminProductActivityDetailsExportDTO> selectAdminProductActivityDetailsExport(@Param("bo") ProductActivitySearchDTO bo);


    List<ProductActivityStockPullDTO> getProductActivityStockPullDTO();

    void updateSupplierProductActivityException(@Param("i") int i,@Param("productSkuCode") String productSkuCode,@Param("warehouseSystemCode") String warehouseSystemCode);

    List<SupplierProductActivityDetails> getSupplierAvailableActivesBySpu(@Param("productCode") String productCode,@Param("site") String site);

    void updateProductActivityStockException(int status, long productSkuStockId,String productSkuCode);

    void updateProductActivityExceptionByStock(@Param("productSkuCode") String productSkuCode);

    void updateProductActivityExceptionByActivity(@Param("supplierActivityCode") String supplierActivityCode);

    IPage<ActivityProductSkuDTO> getActivityProductSkuList(Page<ActivityProductSkuDTO> page,String productSkuCode, String sku, String productName, String site,String tenantId);
}
